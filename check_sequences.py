import sqlite3

def check_sequences():
    conn = sqlite3.connect('database.sqlite')
    cursor = conn.cursor()
    
    # Verificar se a tabela existe
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sequencias_geradas'")
    if not cursor.fetchone():
        print("❌ Tabela 'sequencias_geradas' não existe")
        conn.close()
        return
    
    # Contar total
    cursor.execute('SELECT COUNT(*) FROM sequencias_geradas')
    total = cursor.fetchone()[0]
    print(f'📊 Total de sequências geradas: {total}')
    
    if total > 0:
        # Últimas 5 sequências
        cursor.execute('SELECT data_geracao, tipo, sequencia FROM sequencias_geradas ORDER BY id DESC LIMIT 5')
        print('\n🎯 Últimas 5 sequências:')
        for row in cursor.fetchall():
            print(f'  {row[0]} - {row[1]}: {row[2]}')
    
    conn.close()

if __name__ == "__main__":
    check_sequences()
