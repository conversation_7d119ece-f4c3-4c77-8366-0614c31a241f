import sqlite3
import pandas as pd
import os

def consolidate_database():
    """
    Consolida as tabelas Planilha1, Planilha2, Planilha3 e Planilha4 em uma única tabela 'todos_os_jogos'
    Man<PERSON><PERSON> apenas as colunas: Resultado1, <PERSON>res/Ímpares, Soma
    Remove todas as outras tabelas exceto 'resultados_concursos'
    """

    # Conectar ao database
    db_path = 'database.sqlite'
    if not os.path.exists(db_path):
        print(f"❌ Arquivo {db_path} não encontrado!")
        return

    print("🔄 Iniciando consolidação do database...")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Verificar quais tabelas existem
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [table[0] for table in cursor.fetchall()]
        print(f"📋 Tabelas encontradas: {existing_tables}")

        # Tabelas que queremos consolidar
        planilhas = ['Planilha1', 'Planilha2', 'Planilha3', 'Planilha4']
        colunas_desejadas = ['Resultado1', 'Pares/Ímpares', 'Soma']

        # Lista para armazenar todos os DataFrames
        all_dataframes = []
        total_registros = 0

        # Extrair dados de cada planilha
        for planilha in planilhas:
            if planilha in existing_tables:
                print(f"📊 Processando {planilha}...")

                # Verificar quais colunas existem na tabela
                cursor.execute(f'PRAGMA table_info({planilha})')
                columns_info = cursor.fetchall()
                available_columns = [col[1] for col in columns_info]
                print(f"   Colunas disponíveis: {available_columns}")

                # Selecionar apenas as colunas que existem e que queremos
                columns_to_select = []
                for col in colunas_desejadas:
                    if col in available_columns:
                        columns_to_select.append(f'"{col}"')  # Usar aspas para nomes com caracteres especiais
                    else:
                        print(f"   ⚠️  Coluna '{col}' não encontrada em {planilha}")

                if columns_to_select:
                    # Construir query SQL
                    query = f"SELECT {', '.join(columns_to_select)} FROM {planilha}"
                    print(f"   Query: {query}")

                    # Ler dados
                    df = pd.read_sql_query(query, conn)

                    # Renomear colunas para padronizar (remover aspas)
                    df.columns = colunas_desejadas[:len(df.columns)]

                    print(f"   ✅ {len(df):,} registros extraídos")
                    all_dataframes.append(df)
                    total_registros += len(df)
                else:
                    print(f"   ❌ Nenhuma coluna válida encontrada em {planilha}")
            else:
                print(f"   ⚠️  Tabela {planilha} não encontrada")

        if not all_dataframes:
            print("❌ Nenhum dado foi extraído das planilhas!")
            return

        # Concatenar todos os DataFrames
        print(f"\n🔗 Concatenando {len(all_dataframes)} planilhas...")
        df_consolidado = pd.concat(all_dataframes, ignore_index=True)
        print(f"✅ Total de registros consolidados: {len(df_consolidado):,}")

        # Mostrar amostra dos dados
        print("\n📋 Amostra dos dados consolidados:")
        print(df_consolidado.head().to_string())

        # Criar nova tabela 'todos_os_jogos'
        print(f"\n💾 Criando tabela 'todos_os_jogos'...")
        df_consolidado.to_sql('todos_os_jogos', conn, if_exists='replace', index=False)
        print("✅ Tabela 'todos_os_jogos' criada com sucesso!")

        # Verificar se a tabela foi criada corretamente
        cursor.execute("SELECT COUNT(*) FROM todos_os_jogos")
        count = cursor.fetchone()[0]
        print(f"✅ Verificação: {count:,} registros na nova tabela")

        # Listar todas as tabelas para decidir quais deletar
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        all_tables = [table[0] for table in cursor.fetchall()]

        # Tabelas que devem ser mantidas
        tabelas_manter = ['todos_os_jogos', 'resultados_concursos']

        # Deletar tabelas desnecessárias
        print(f"\n🗑️  Removendo tabelas desnecessárias...")
        for table in all_tables:
            if table not in tabelas_manter:
                print(f"   Deletando tabela: {table}")
                cursor.execute(f"DROP TABLE IF EXISTS {table}")

        # Commit das mudanças
        conn.commit()

        # Verificar estrutura final
        print(f"\n✅ CONSOLIDAÇÃO CONCLUÍDA!")
        print(f"📊 Estrutura final do database:")

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        final_tables = cursor.fetchall()

        for table in final_tables:
            table_name = table[0]
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            count = cursor.fetchone()[0]
            print(f"   📋 {table_name}: {count:,} registros")

    except Exception as e:
        print(f"❌ Erro durante a consolidação: {str(e)}")
        conn.rollback()

    finally:
        conn.close()

def add_missing_columns():
    """
    Adiciona colunas necessárias às tabelas
    """
    print("🔧 Adicionando colunas necessárias...")
    conn = sqlite3.connect('database.sqlite')
    cursor = conn.cursor()

    try:
        # Adicionar coluna jogo_finalizado à tabela todos_os_jogos
        try:
            cursor.execute("ALTER TABLE todos_os_jogos ADD COLUMN jogo_finalizado BOOLEAN DEFAULT FALSE")
            print("✅ Coluna 'jogo_finalizado' adicionada à tabela 'todos_os_jogos'")
        except Exception as e:
            if "duplicate column name" in str(e).lower():
                print("ℹ️  Coluna 'jogo_finalizado' já existe em 'todos_os_jogos'")
            else:
                print(f"❌ Erro ao adicionar coluna 'jogo_finalizado': {e}")

        # Adicionar coluna onda_direcao à tabela resultados_concursos
        try:
            cursor.execute("ALTER TABLE resultados_concursos ADD COLUMN onda_direcao TEXT")
            print("✅ Coluna 'onda_direcao' adicionada à tabela 'resultados_concursos'")
        except Exception as e:
            if "duplicate column name" in str(e).lower():
                print("ℹ️  Coluna 'onda_direcao' já existe em 'resultados_concursos'")
            else:
                print(f"❌ Erro ao adicionar coluna 'onda_direcao': {e}")

        conn.commit()

    except Exception as e:
        print(f"❌ Erro geral: {e}")
        conn.rollback()

    finally:
        conn.close()

if __name__ == "__main__":
    consolidate_database()
    add_missing_columns()
