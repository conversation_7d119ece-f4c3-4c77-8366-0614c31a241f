import sqlite3
import random
import pandas as pd
from datetime import datetime, timedelta
import itertools
from typing import List, Tu<PERSON>, Set

class LotofacilGenerator:
    def __init__(self, db_path='database.sqlite'):
        self.db_path = db_path
        
        # Faixas de posições e suas características
        self.faixas = {
            'faixa_1': {
                'range': (1000000, 1500000),
                'percentage': 15.35,
                'mais_frequentes': [1, 2, 3, 13, 10, 24],
                'menos_frequentes': [2, 17, 21, 23, 8],
                'sequencias_iniciais': [1, 2, 5, 10, 13, 24]
            },
            'faixa_2': {
                'range': (1500000, 2000000),
                'percentage': 15.26,
                'mais_frequentes': [1, 20, 10, 17, 21],
                'menos_frequentes': [2, 3, 4, 6, 23]
            },
            'faixa_3': {
                'range': (2000000, 2500000),
                'percentage': 14.65,
                'mais_frequentes': [2, 3, 20, 11, 25],
                'menos_frequentes': [8, 6, 13, 12, 17],
                'sequencias_iniciais': [2, 3, 11, 20, 25]
            },
            'faixa_4': {
                'range': (2500000, 3000000),
                'percentage': 16.02,
                'mais_frequentes': [4, 10, 20, 19, 22],
                'menos_frequentes': [3, 2, 17, 15, 7]
            }
        }
        
        # Sequências com melhor desempenho histórico
        self.sequencias_historicas = [
            {
                'numeros': [1,2,3,5,6,7,12,13,15,16,17,18,20,21,23],
                'performance': {'15_pontos': 1, '14_pontos': 3},
                'status': 'ja_ganhou_15'  # Nunca mais pontua
            },
            {
                'numeros': [1,2,3,4,6,9,13,14,15,17,19,20,21,22,25],
                'performance': {'15_pontos': 0, '14_pontos': 5},
                'status': 'ativo'
            },
            {
                'numeros': [1,3,5,7,8,9,11,12,14,15,16,20,21,23,25],
                'performance': {'15_pontos': 1, '14_pontos': 2},
                'status': 'ja_ganhou_15'
            },
            {
                'numeros': [1,3,5,6,8,9,10,12,13,15,16,19,21,23,25],
                'performance': {'15_pontos': 1, '14_pontos': 2},
                'status': 'ja_ganhou_15'
            },
            {
                'numeros': [1,4,7,11,13,14,16,17,19,20,21,22,23,24,25],
                'performance': {'15_pontos': 1, '14_pontos': 2},
                'status': 'ja_ganhou_15'
            },
            {
                'numeros': [1,2,7,9,10,11,12,13,15,16,17,20,23,24,25],
                'performance': {'15_pontos': 1, '14_pontos': 2},
                'status': 'ja_ganhou_15'
            }
        ]
        
        # Números obrigatórios para 4 sequências
        self.numeros_obrigatorios = [1, 3, 23, 25]
    
    def get_connection(self):
        """Retorna conexão com o banco de dados"""
        return sqlite3.connect(self.db_path)
    
    def get_resultados_sorteados(self) -> Set[str]:
        """Busca todos os resultados já sorteados"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT resultado FROM resultados_concursos")
            resultados = cursor.fetchall()
            
            conn.close()
            
            return {resultado[0] for resultado in resultados}
        except Exception as e:
            print(f"❌ Erro ao buscar resultados sorteados: {e}")
            return set()
    
    def get_posicoes_validas(self) -> List[int]:
        """Busca posições válidas na faixa de 1M a 3M, excluindo terminadas em zero"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Buscar posições na faixa de 1M a 3M
            cursor.execute("""
                SELECT rowid FROM todos_os_jogos 
                WHERE rowid BETWEEN 1000000 AND 3000000
                AND rowid % 1000000 != 0  -- Excluir terminadas em zero
                AND jogo_finalizado = 0   -- Apenas não finalizados
            """)
            
            posicoes = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            return posicoes
        except Exception as e:
            print(f"❌ Erro ao buscar posições válidas: {e}")
            return []
    
    def get_jogo_por_posicao(self, posicao: int) -> str:
        """Busca o jogo na posição específica"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT Resultado1 FROM todos_os_jogos WHERE rowid = ?", (posicao,))
            result = cursor.fetchone()
            
            conn.close()
            
            return result[0] if result else None
        except Exception as e:
            print(f"❌ Erro ao buscar jogo na posição {posicao}: {e}")
            return None
    
    def determinar_faixa(self, posicao: int) -> str:
        """Determina qual faixa a posição pertence"""
        for faixa_nome, faixa_data in self.faixas.items():
            inicio, fim = faixa_data['range']
            if inicio <= posicao < fim:
                return faixa_nome
        return None
    
    def gerar_sequencia_com_obrigatorios(self) -> List[int]:
        """Gera sequência com números obrigatórios [1,3,23,25] + 11 aleatórios"""
        sequencia = self.numeros_obrigatorios.copy()
        
        # Números disponíveis (excluindo os obrigatórios)
        disponiveis = [n for n in range(1, 26) if n not in self.numeros_obrigatorios]
        
        # Adicionar 11 números aleatórios
        aleatorios = random.sample(disponiveis, 11)
        sequencia.extend(aleatorios)
        
        return sorted(sequencia)
    
    def gerar_sequencia_por_faixa(self, faixa_nome: str) -> List[int]:
        """Gera sequência baseada nas características da faixa"""
        faixa = self.faixas[faixa_nome]
        sequencia = []
        
        # Começar com sequência inicial se disponível
        if 'sequencias_iniciais' in faixa:
            sequencia.extend(faixa['sequencias_iniciais'][:3])  # Primeiros 3
        
        # Adicionar números mais frequentes
        mais_freq = [n for n in faixa['mais_frequentes'] if n not in sequencia]
        sequencia.extend(mais_freq[:4])  # Mais 4
        
        # Adicionar alguns menos frequentes
        menos_freq = [n for n in faixa['menos_frequentes'] if n not in sequencia]
        sequencia.extend(menos_freq[:2])  # Mais 2
        
        # Completar com números aleatórios
        todos_numeros = list(range(1, 26))
        disponiveis = [n for n in todos_numeros if n not in sequencia]
        
        faltam = 15 - len(sequencia)
        if faltam > 0:
            aleatorios = random.sample(disponiveis, min(faltam, len(disponiveis)))
            sequencia.extend(aleatorios)
        
        return sorted(sequencia[:15])
    
    def formatar_sequencia(self, numeros: List[int]) -> str:
        """Formata sequência no padrão 01,02,03..."""
        return ','.join([f"{n:02d}" for n in sorted(numeros)])
    
    def sequencia_ja_sorteada(self, sequencia: str, resultados_sorteados: Set[str]) -> bool:
        """Verifica se a sequência já foi sorteada"""
        return sequencia in resultados_sorteados
    
    def gerar_10_sequencias(self) -> List[dict]:
        """Gera 10 sequências seguindo as regras especificadas"""
        print("🎲 Gerando 10 sequências para o próximo sorteio...")
        
        # Buscar resultados já sorteados
        resultados_sorteados = self.get_resultados_sorteados()
        print(f"📊 {len(resultados_sorteados)} resultados já sorteados serão excluídos")
        
        # Buscar posições válidas
        posicoes_validas = self.get_posicoes_validas()
        print(f"🎯 {len(posicoes_validas)} posições válidas encontradas (1M-3M, excluindo zeros)")
        
        sequencias_geradas = []
        tentativas = 0
        max_tentativas = 1000
        
        # Gerar 4 sequências com números obrigatórios
        print("\n🔸 Gerando 4 sequências com números obrigatórios [1,3,23,25]...")
        for i in range(4):
            while tentativas < max_tentativas:
                tentativas += 1
                sequencia_nums = self.gerar_sequencia_com_obrigatorios()
                sequencia_str = self.formatar_sequencia(sequencia_nums)
                
                if not self.sequencia_ja_sorteada(sequencia_str, resultados_sorteados):
                    sequencias_geradas.append({
                        'tipo': 'obrigatoria',
                        'numeros': sequencia_nums,
                        'formatada': sequencia_str,
                        'descricao': f'Sequência obrigatória {i+1} com [1,3,23,25]'
                    })
                    print(f"  ✅ Sequência {i+1}: {sequencia_str}")
                    break
        
        # Gerar 6 sequências baseadas nas faixas
        print("\n🔸 Gerando 6 sequências baseadas nas faixas de frequência...")
        faixas_nomes = list(self.faixas.keys())
        
        for i in range(6):
            while tentativas < max_tentativas:
                tentativas += 1
                
                # Escolher faixa aleatoriamente (com peso baseado na porcentagem)
                faixa_escolhida = random.choice(faixas_nomes)
                
                sequencia_nums = self.gerar_sequencia_por_faixa(faixa_escolhida)
                sequencia_str = self.formatar_sequencia(sequencia_nums)
                
                if not self.sequencia_ja_sorteada(sequencia_str, resultados_sorteados):
                    sequencias_geradas.append({
                        'tipo': 'faixa',
                        'faixa': faixa_escolhida,
                        'numeros': sequencia_nums,
                        'formatada': sequencia_str,
                        'descricao': f'Sequência baseada na {faixa_escolhida}'
                    })
                    print(f"  ✅ Sequência {len(sequencias_geradas)}: {sequencia_str} ({faixa_escolhida})")
                    break
        
        print(f"\n🎯 {len(sequencias_geradas)} sequências geradas com sucesso!")
        print(f"🔄 Total de tentativas: {tentativas}")
        
        return sequencias_geradas
    
    def salvar_sequencias_geradas(self, sequencias: List[dict], data_geracao: datetime):
        """Salva as sequências geradas no banco de dados"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Criar tabela se não existir
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sequencias_geradas (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    data_geracao DATE,
                    tipo TEXT,
                    faixa TEXT,
                    sequencia TEXT,
                    descricao TEXT,
                    usado BOOLEAN DEFAULT FALSE,
                    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Inserir sequências
            for seq in sequencias:
                cursor.execute("""
                    INSERT INTO sequencias_geradas 
                    (data_geracao, tipo, faixa, sequencia, descricao)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    data_geracao.date(),
                    seq['tipo'],
                    seq.get('faixa', ''),
                    seq['formatada'],
                    seq['descricao']
                ))
            
            conn.commit()
            conn.close()
            
            print(f"✅ {len(sequencias)} sequências salvas no banco de dados!")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao salvar sequências: {e}")
            return False
    
    def gerar_sequencias_do_dia(self, data_alvo: datetime = None) -> bool:
        """Gera sequências para um dia específico"""
        if data_alvo is None:
            data_alvo = datetime.now()
        
        print(f"🎲 Gerando sequências para {data_alvo.strftime('%d/%m/%Y')}")
        
        # Verificar se é domingo
        if data_alvo.weekday() == 6:  # 6 = domingo
            print("🚫 Domingo - não há sorteio")
            return False
        
        # Gerar sequências
        sequencias = self.gerar_10_sequencias()
        
        if len(sequencias) == 10:
            # Salvar no banco
            return self.salvar_sequencias_geradas(sequencias, data_alvo)
        else:
            print(f"❌ Erro: apenas {len(sequencias)} sequências geradas (esperado: 10)")
            return False

def main():
    """Função principal para teste"""
    generator = LotofacilGenerator()
    
    # Gerar sequências para hoje
    sucesso = generator.gerar_sequencias_do_dia()
    
    if sucesso:
        print("\n🎉 Sequências geradas com sucesso!")
    else:
        print("\n❌ Erro ao gerar sequências")

if __name__ == "__main__":
    main()
