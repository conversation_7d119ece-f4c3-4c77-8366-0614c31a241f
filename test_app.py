import sqlite3
import pandas as pd

# Teste básico da conexão com o banco
try:
    conn = sqlite3.connect('database.sqlite')
    cursor = conn.cursor()
    
    print("✅ Conexão com banco estabelecida")
    
    # Verificar tabelas
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"📋 Tabelas encontradas: {[t[0] for t in tables]}")
    
    # Verificar estrutura da tabela todos_os_jogos
    cursor.execute("PRAGMA table_info(todos_os_jogos)")
    columns = cursor.fetchall()
    print(f"🔧 Colunas em todos_os_jogos: {[col[1] for col in columns]}")
    
    # Verificar estrutura da tabela resultados_concursos
    cursor.execute("PRAGMA table_info(resultados_concursos)")
    columns = cursor.fetchall()
    print(f"🔧 Colunas em resultados_concursos: {[col[1] for col in columns]}")
    
    # Teste de busca
    cursor.execute("SELECT COUNT(*) FROM todos_os_jogos")
    count = cursor.fetchone()[0]
    print(f"📊 Total de jogos: {count:,}")
    
    cursor.execute("SELECT COUNT(*) FROM resultados_concursos")
    count = cursor.fetchone()[0]
    print(f"🏆 Total de concursos: {count:,}")
    
    conn.close()
    print("✅ Teste concluído com sucesso!")
    
except Exception as e:
    print(f"❌ Erro: {e}")
