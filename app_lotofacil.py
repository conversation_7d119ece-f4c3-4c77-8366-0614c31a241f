import streamlit as st
import sqlite3
import pandas as pd
from lotofacil_updater import LotofacilUpdater
from lotofacil_generator import LotofacilGenerator
from lotofacil_scheduler import LotofacilScheduler

# Configuração da página
st.set_page_config(
    page_title="🎲 Loto Fácil - Gerenciador de Jogos",
    page_icon="🎲",
    layout="wide",
    initial_sidebar_state="expanded"
)

class DatabaseManager:
    def __init__(self, db_path='database.sqlite'):
        self.db_path = db_path

    def get_connection(self):
        return sqlite3.connect(self.db_path)

    def execute_query(self, query, params=None):
        """Executa uma query SELECT e retorna DataFrame"""
        try:
            conn = self.get_connection()
            if params:
                df = pd.read_sql_query(query, conn, params=params)
            else:
                df = pd.read_sql_query(query, conn)
            conn.close()
            return df
        except Exception as e:
            st.error(f"Erro na consulta: {e}")
            return pd.DataFrame()

    def execute_command(self, query, params=None):
        """Executa comandos INSERT, UPDATE, DELETE"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            st.error(f"Erro no comando: {e}")
            return False

class LotoFacilProcessor:
    @staticmethod
    def validate_numbers(numbers_str):
        """Valida se os números estão no formato correto"""
        try:
            # Remove espaços e divide por vírgula
            numbers = [int(n.strip()) for n in numbers_str.split(',')]

            # Verificações
            if len(numbers) != 15:
                return False, f"Deve ter exatamente 15 números. Você digitou {len(numbers)}"

            if any(n < 1 or n > 25 for n in numbers):
                return False, "Números devem estar entre 1 e 25"

            if len(set(numbers)) != 15:
                return False, "Não pode haver números repetidos"

            return True, numbers
        except:
            return False, "Formato inválido. Use: 01,02,03,04,05,06,07,08,09,10,11,12,13,14,15"

    @staticmethod
    def format_numbers(numbers):
        """Formata números para o padrão 01,02,03..."""
        sorted_numbers = sorted(numbers)
        return ','.join([f"{n:02d}" for n in sorted_numbers])

    @staticmethod
    def calculate_pairs_odds(numbers):
        """Calcula distribuição de pares e ímpares"""
        pairs = sum(1 for n in numbers if n % 2 == 0)
        odds = 15 - pairs
        return f"Par:{pairs} Impar:{odds}"

    @staticmethod
    def calculate_sum(numbers):
        """Calcula soma dos números"""
        return sum(numbers)

    @staticmethod
    def find_game_position(db_manager, resultado_formatado):
        """Encontra a posição do jogo na tabela todos_os_jogos"""
        query = "SELECT rowid FROM todos_os_jogos WHERE Resultado1 = ? LIMIT 1"
        df = db_manager.execute_query(query, (resultado_formatado,))
        return df.iloc[0]['rowid'] if not df.empty else None

    @staticmethod
    def calculate_wave_direction(db_manager, current_position):
        """Calcula a direção da onda baseada na posição anterior"""
        # Buscar último resultado
        query = """
        SELECT id_posicao_database
        FROM resultados_concursos
        WHERE id_posicao_database IS NOT NULL
        ORDER BY id_concurso DESC
        LIMIT 1
        """
        df = db_manager.execute_query(query)

        if df.empty:
            return "0", "Início"  # Primeiro jogo

        last_position = df.iloc[0]['id_posicao_database']
        difference = current_position - last_position

        if difference > 0:
            return str(difference), "Subiu"
        elif difference < 0:
            return str(abs(difference)), "Desceu"
        else:
            return "0", "Manteve"

# Inicializar gerenciador
db_manager = DatabaseManager()

# Sidebar
st.sidebar.title("🎲 Loto Fácil")
st.sidebar.markdown("---")

menu_options = [
    "🏠 Dashboard",
    "➕ Inserir Novo Jogo",
    "🔄 Atualizar Últimos Jogos",
    "🎲 Gerador de Sequências",
    "🗑️ Deletar Jogo",
    "📊 Consultar Resultados",
    "🎯 Análise de Jogos",
    "⚙️ Configurações"
]

selected_menu = st.sidebar.selectbox("Navegação:", menu_options)

# Dashboard
if selected_menu == "🏠 Dashboard":
    st.title("🏠 Dashboard - Loto Fácil")

    col1, col2, col3, col4 = st.columns(4)

    # Estatísticas gerais
    with col1:
        total_jogos = db_manager.execute_query("SELECT COUNT(*) as total FROM todos_os_jogos")
        st.metric("🎲 Total de Jogos", f"{total_jogos.iloc[0]['total']:,}")

    with col2:
        total_concursos = db_manager.execute_query("SELECT COUNT(*) as total FROM resultados_concursos")
        st.metric("🏆 Concursos Realizados", f"{total_concursos.iloc[0]['total']:,}")

    with col3:
        jogos_finalizados = db_manager.execute_query("SELECT COUNT(*) as total FROM todos_os_jogos WHERE jogo_finalizado = 1")
        st.metric("✅ Jogos Finalizados", f"{jogos_finalizados.iloc[0]['total']:,}")

    with col4:
        ultimo_concurso = db_manager.execute_query("SELECT MAX(id_concurso) as ultimo FROM resultados_concursos")
        st.metric("🎯 Último Concurso", f"{ultimo_concurso.iloc[0]['ultimo']}")

    st.markdown("---")

    # Últimos resultados
    st.subheader("🏆 Últimos 10 Resultados")
    ultimos_resultados = db_manager.execute_query("""
        SELECT id_concurso, resultado, pares_impares, soma_numeros_resultaado,
               onda_variavel, onda_direcao
        FROM resultados_concursos
        ORDER BY id_concurso DESC
        LIMIT 10
    """)

    if not ultimos_resultados.empty:
        st.dataframe(ultimos_resultados, use_container_width=True)
    else:
        st.info("Nenhum resultado encontrado.")

# Inserir Novo Jogo
elif selected_menu == "➕ Inserir Novo Jogo":
    st.title("➕ Inserir Novo Jogo")

    # Buscar próximo ID automaticamente
    ultimo_id = db_manager.execute_query("SELECT MAX(id_concurso) as ultimo FROM resultados_concursos")
    if not ultimo_id.empty and ultimo_id.iloc[0]['ultimo'] is not None:
        proximo_id = ultimo_id.iloc[0]['ultimo'] + 1
    else:
        proximo_id = 1  # Primeiro jogo

    with st.form("novo_jogo_form"):
        st.subheader("📝 Dados do Concurso")

        col1, col2 = st.columns(2)

        with col1:
            st.text_input("ID do Concurso:", value=str(proximo_id), disabled=True, key="id_display")
            id_concurso = proximo_id  # Usar o ID automático

        with col2:
            st.success(f"🎯 Próximo concurso: {proximo_id}")

        st.markdown("---")

        resultado_input = st.text_input(
            "🎯 Resultado (15 números):",
            placeholder="01,02,03,04,05,06,07,08,09,10,11,12,13,14,15",
            help="Digite 15 números de 1 a 25, separados por vírgula"
        )

        submitted = st.form_submit_button("🚀 Processar e Inserir Jogo", type="primary")

        if submitted:
            if not resultado_input:
                st.error("❌ Por favor, informe o resultado")
            else:
                # Validar números
                is_valid, validation_result = LotoFacilProcessor.validate_numbers(resultado_input)

                if not is_valid:
                    st.error(f"❌ {validation_result}")
                else:
                    numbers = validation_result

                    # Processar dados
                    resultado_formatado = LotoFacilProcessor.format_numbers(numbers)
                    pares_impares = LotoFacilProcessor.calculate_pairs_odds(numbers)
                    soma = LotoFacilProcessor.calculate_sum(numbers)

                    # Encontrar posição na tabela todos_os_jogos
                    posicao = LotoFacilProcessor.find_game_position(db_manager, resultado_formatado)

                    if posicao is None:
                        st.error("❌ Este jogo não foi encontrado na tabela de jogos possíveis!")
                    else:
                        # Calcular onda
                        onda_valor, onda_direcao = LotoFacilProcessor.calculate_wave_direction(db_manager, posicao)

                        # Mostrar preview
                        st.success("✅ Dados processados com sucesso!")

                        col1, col2 = st.columns(2)
                        with col1:
                            st.write("**📊 Dados Calculados:**")
                            st.write(f"- Resultado: {resultado_formatado}")
                            st.write(f"- Pares/Ímpares: {pares_impares}")
                            st.write(f"- Soma: {soma}")

                        with col2:
                            st.write(f"- Posição no DB: {posicao}")
                            st.write(f"- Onda Variável: {onda_valor}")
                            st.write(f"- Direção: {onda_direcao}")

                        # Confirmar inserção
                        if st.button("✅ Confirmar e Inserir no Banco"):
                            # Inserir na tabela resultados_concursos
                            insert_query = """
                            INSERT INTO resultados_concursos
                            (id_concurso, resultado, pares_impares, soma_numeros_resultaado,
                             id_posicao_database, onda_variavel, onda_direcao)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                            """

                            success = db_manager.execute_command(insert_query, [
                                id_concurso, resultado_formatado, pares_impares, soma,
                                posicao, onda_valor, onda_direcao
                            ])

                            if success:
                                # Marcar jogo como finalizado na tabela todos_os_jogos
                                update_query = "UPDATE todos_os_jogos SET jogo_finalizado = 1 WHERE Resultado1 = ?"
                                db_manager.execute_command(update_query, [resultado_formatado])

                                st.success("🎉 Jogo inserido com sucesso!")
                                st.balloons()
                            else:
                                st.error("❌ Erro ao inserir o jogo")

# Atualizar Últimos Jogos
elif selected_menu == "🔄 Atualizar Últimos Jogos":
    st.title("🔄 Atualizar Últimos Jogos")
    st.info("🌐 Esta funcionalidade busca automaticamente os últimos resultados da Lotofácil e atualiza sua base de dados.")

    # Inicializar o updater
    updater = LotofacilUpdater()

    col1, col2 = st.columns(2)

    with col1:
        # Mostrar status atual
        st.subheader("📊 Status Atual")

        try:
            last_local = updater.get_last_contest_id()
            st.write(f"**Último concurso no banco:** {last_local}")

            with st.spinner("🌐 Verificando último concurso disponível..."):
                latest_api = updater.get_latest_contest_from_api()

            if latest_api:
                st.write(f"**Último concurso disponível:** {latest_api}")

                missing_count = latest_api - last_local
                if missing_count > 0:
                    st.warning(f"⚠️ **{missing_count} concursos** precisam ser atualizados!")
                    st.write(f"📥 Concursos faltando: {last_local + 1} até {latest_api}")
                else:
                    st.success("✅ Base de dados está atualizada!")
            else:
                st.error("❌ Erro ao verificar API")
        except Exception as e:
            st.error(f"❌ Erro ao verificar status: {e}")

    with col2:
        # Botão de atualização
        st.subheader("🚀 Atualização")

        if st.button("🔄 Atualizar Base de Dados", type="primary", use_container_width=True):

            # Container para mostrar progresso
            progress_container = st.container()

            with progress_container:
                st.write("🔄 **Iniciando atualização...**")

                # Barra de progresso
                progress_bar = st.progress(0)
                status_text = st.empty()

                def update_progress(current, total, contest_id):
                    progress = current / total
                    progress_bar.progress(progress)
                    status_text.text(f"📥 Baixando concurso {contest_id} ({current}/{total})")

                try:
                    # Executar atualização
                    success, message = updater.update_missing_contests(progress_callback=update_progress)

                    # Limpar progresso
                    progress_bar.empty()
                    status_text.empty()

                    if success:
                        st.success(f"✅ {message}")
                        st.balloons()

                        # Mostrar estatísticas atualizadas
                        st.subheader("📈 Estatísticas Atualizadas")

                        # Recarregar dados
                        new_last_local = updater.get_last_contest_id()
                        total_concursos = db_manager.execute_query("SELECT COUNT(*) as total FROM resultados_concursos")

                        col1_stats, col2_stats = st.columns(2)

                        with col1_stats:
                            st.metric("🎯 Último Concurso", new_last_local)

                        with col2_stats:
                            if not total_concursos.empty:
                                st.metric("🏆 Total de Concursos", total_concursos.iloc[0]['total'])

                        # Sugerir recarregar a página
                        st.info("💡 **Dica:** Recarregue a página para ver as atualizações no Dashboard!")

                    else:
                        st.error(f"❌ {message}")

                except Exception as e:
                    progress_bar.empty()
                    status_text.empty()
                    st.error(f"❌ Erro durante a atualização: {e}")

    st.markdown("---")

    # Seção de informações
    with st.expander("ℹ️ Como funciona a atualização"):
        st.write("""
        **🔍 Processo de Atualização:**

        1. **Verificação:** O sistema verifica qual é o último concurso cadastrado no seu banco de dados
        2. **Consulta API:** Busca na API pública da Lotofácil qual é o último concurso disponível
        3. **Download:** Baixa automaticamente todos os concursos que estão faltando
        4. **Processamento:** Para cada concurso:
           - Formata os números no padrão correto (01,02,03...)
           - Calcula pares/ímpares automaticamente
           - Calcula a soma dos números
           - Encontra a posição na tabela de jogos possíveis
           - Calcula a onda (direção em relação ao jogo anterior)
        5. **Inserção:** Insere os dados na tabela `resultados_concursos`
        6. **Finalização:** Marca os jogos como finalizados na tabela `todos_os_jogos`

        **🌐 Fonte dos Dados:**
        - API: `https://loteriascaixa-api.herokuapp.com/api/lotofacil`
        - Dados oficiais da Caixa Econômica Federal
        - Atualização em tempo real

        **⚡ Vantagens:**
        - Sem necessidade de captcha ou web scraping complexo
        - Dados sempre atualizados e confiáveis
        - Processamento automático de todos os cálculos
        - Integração perfeita com sua base de dados existente
        """)

# Gerador de Sequências
elif selected_menu == "🎲 Gerador de Sequências":
    st.title("🎲 Gerador de Sequências Inteligente")
    st.info("🧠 Gera sequências baseadas em análise estatística e padrões históricos da Lotofácil")

    # Inicializar gerador e scheduler
    generator = LotofacilGenerator()
    scheduler = LotofacilScheduler()

    tab1, tab2, tab3 = st.tabs(["🎯 Gerar Agora", "📅 Agendamentos", "📊 Histórico"])

    with tab1:
        st.subheader("🎯 Geração Manual de Sequências")

        col1, col2 = st.columns([2, 1])

        with col1:
            st.write("**🔍 Regras do Gerador:**")
            st.write("""
            - **4 sequências** com números obrigatórios: [1, 3, 23, 25]
            - **6 sequências** baseadas nas faixas de frequência:
              - Faixa 1M-1.5M: Mais frequentes [1,2,3,13,10,24]
              - Faixa 1.5M-2M: Mais frequentes [1,20,10,17,21]
              - Faixa 2M-2.5M: Mais frequentes [2,3,20,11,25]
              - Faixa 2.5M-3M: Mais frequentes [4,10,20,19,22]
            - **Exclusões automáticas:**
              - Jogos já sorteados
              - Posições que terminam em zero
              - Sequências que já fizeram 15 pontos
            """)

        with col2:
            if st.button("🎲 Gerar 10 Sequências", type="primary", use_container_width=True):
                with st.spinner("🔄 Gerando sequências inteligentes..."):
                    try:
                        sequencias = generator.gerar_10_sequencias()

                        if len(sequencias) == 10:
                            st.success("✅ 10 sequências geradas com sucesso!")

                            # Salvar no banco
                            from datetime import datetime
                            sucesso_save = generator.salvar_sequencias_geradas(sequencias, datetime.now())

                            if sucesso_save:
                                st.info("💾 Sequências salvas no banco de dados")

                            # Mostrar sequências geradas
                            st.subheader("🎯 Sequências Geradas")

                            # Separar por tipo
                            obrigatorias = [s for s in sequencias if s['tipo'] == 'obrigatoria']
                            faixas = [s for s in sequencias if s['tipo'] == 'faixa']

                            # Mostrar obrigatórias
                            st.write("**🔸 Sequências com números obrigatórios [1,3,23,25]:**")
                            for i, seq in enumerate(obrigatorias, 1):
                                st.code(f"{i}. {seq['formatada']}")

                            # Mostrar por faixas
                            st.write("**🔸 Sequências baseadas em faixas de frequência:**")
                            for i, seq in enumerate(faixas, 5):
                                faixa_info = seq.get('faixa', 'N/A')
                                st.code(f"{i}. {seq['formatada']} ({faixa_info})")

                        else:
                            st.error(f"❌ Erro: apenas {len(sequencias)} sequências geradas")

                    except Exception as e:
                        st.error(f"❌ Erro ao gerar sequências: {e}")

    with tab2:
        st.subheader("📅 Agendamentos Automáticos")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**⏰ Horários Configurados:**")
            st.write("""
            - **00:00** - Gerar sequências (Segunda a Sábado)
            - **06:00** - Atualizar base de dados
            - **20:30** - Verificar novos resultados
            """)

            # Status dos agendamentos
            try:
                status = scheduler.status_agendamentos()

                if status['total_jobs'] > 0:
                    st.success(f"✅ {status['total_jobs']} agendamentos ativos")

                    if status['proximo_job']:
                        st.info(f"🕐 Próximo: {status['proximo_job']['funcao']} em {status['proximo_job']['proximo_run']}")
                else:
                    st.warning("⚠️ Nenhum agendamento ativo")

            except Exception as e:
                st.error(f"❌ Erro ao verificar agendamentos: {e}")

        with col2:
            st.write("**🎮 Controles Manuais:**")

            if st.button("▶️ Iniciar Agendamentos", use_container_width=True):
                try:
                    scheduler.iniciar_scheduler()
                    st.success("✅ Agendamentos iniciados!")
                except Exception as e:
                    st.error(f"❌ Erro: {e}")

            if st.button("⏹️ Parar Agendamentos", use_container_width=True):
                try:
                    scheduler.parar_scheduler()
                    st.success("✅ Agendamentos parados!")
                except Exception as e:
                    st.error(f"❌ Erro: {e}")

            st.markdown("---")

            st.write("**🧪 Testes Manuais:**")

            if st.button("🎲 Testar Geração", use_container_width=True):
                with st.spinner("Testando..."):
                    sucesso = scheduler.executar_agora("gerar")
                    if sucesso:
                        st.success("✅ Teste de geração OK")
                    else:
                        st.error("❌ Erro no teste")

            if st.button("🔄 Testar Atualização", use_container_width=True):
                with st.spinner("Testando..."):
                    sucesso = scheduler.executar_agora("atualizar")
                    if sucesso:
                        st.success("✅ Teste de atualização OK")
                    else:
                        st.error("❌ Erro no teste")

    with tab3:
        st.subheader("📊 Histórico de Sequências Geradas")

        # Buscar sequências salvas
        try:
            historico = db_manager.execute_query("""
                SELECT data_geracao, tipo, faixa, sequencia, descricao, usado
                FROM sequencias_geradas
                ORDER BY data_geracao DESC, id DESC
                LIMIT 50
            """)

            if not historico.empty:
                # Filtros
                col1, col2, col3 = st.columns(3)

                with col1:
                    tipos_unicos = ['Todos'] + historico['tipo'].unique().tolist()
                    tipo_filtro = st.selectbox("Filtrar por tipo:", tipos_unicos)

                with col2:
                    datas_unicas = ['Todas'] + historico['data_geracao'].unique().tolist()
                    data_filtro = st.selectbox("Filtrar por data:", datas_unicas)

                with col3:
                    usado_filtro = st.selectbox("Status:", ['Todos', 'Usados', 'Não Usados'])

                # Aplicar filtros
                df_filtrado = historico.copy()

                if tipo_filtro != 'Todos':
                    df_filtrado = df_filtrado[df_filtrado['tipo'] == tipo_filtro]

                if data_filtro != 'Todas':
                    df_filtrado = df_filtrado[df_filtrado['data_geracao'] == data_filtro]

                if usado_filtro == 'Usados':
                    df_filtrado = df_filtrado[df_filtrado['usado'] == True]
                elif usado_filtro == 'Não Usados':
                    df_filtrado = df_filtrado[df_filtrado['usado'] == False]

                # Mostrar resultados
                if not df_filtrado.empty:
                    st.dataframe(df_filtrado, use_container_width=True)

                    # Estatísticas
                    st.subheader("📈 Estatísticas")
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.metric("Total de Sequências", len(df_filtrado))

                    with col2:
                        usadas = len(df_filtrado[df_filtrado['usado'] == True])
                        st.metric("Sequências Usadas", usadas)

                    with col3:
                        nao_usadas = len(df_filtrado[df_filtrado['usado'] == False])
                        st.metric("Sequências Disponíveis", nao_usadas)
                else:
                    st.info("Nenhuma sequência encontrada com os filtros aplicados")
            else:
                st.info("Nenhuma sequência gerada ainda. Use a aba 'Gerar Agora' para criar suas primeiras sequências!")

        except Exception as e:
            st.error(f"❌ Erro ao buscar histórico: {e}")

# Deletar Jogo
elif selected_menu == "🗑️ Deletar Jogo":
    st.title("🗑️ Deletar Jogo")
    st.warning("⚠️ **ATENÇÃO:** Esta ação é irreversível! Use com cuidado.")

    tab1, tab2 = st.tabs(["🔍 Buscar por ID", "📋 Listar Últimos"])

    with tab1:
        st.subheader("🔍 Buscar Jogo por ID do Concurso")

        col1, col2 = st.columns([2, 1])

        with col1:
            id_busca = st.number_input("ID do Concurso para deletar:", min_value=1, step=1, key="delete_id")

        with col2:
            if st.button("🔍 Buscar Jogo", type="secondary"):
                if id_busca:
                    # Buscar o jogo
                    query = """
                    SELECT id_concurso, resultado, pares_impares, soma_numeros_resultaado,
                           id_posicao_database, onda_variavel, onda_direcao
                    FROM resultados_concursos
                    WHERE id_concurso = ?
                    """
                    df_jogo = db_manager.execute_query(query, [id_busca])

                    if not df_jogo.empty:
                        st.success(f"✅ Jogo encontrado: Concurso {id_busca}")

                        # Mostrar detalhes do jogo
                        jogo = df_jogo.iloc[0]

                        col1, col2 = st.columns(2)
                        with col1:
                            st.write("**📊 Detalhes do Jogo:**")
                            st.write(f"- **ID Concurso:** {jogo['id_concurso']}")
                            st.write(f"- **Resultado:** {jogo['resultado']}")
                            st.write(f"- **Pares/Ímpares:** {jogo['pares_impares']}")
                            st.write(f"- **Soma:** {jogo['soma_numeros_resultaado']}")

                        with col2:
                            st.write(f"- **Posição DB:** {jogo['id_posicao_database']}")
                            st.write(f"- **Onda Variável:** {jogo['onda_variavel']}")
                            st.write(f"- **Direção:** {jogo['onda_direcao']}")

                        st.markdown("---")

                        # Confirmação de exclusão
                        st.error("🚨 **CONFIRME A EXCLUSÃO:**")

                        col1, col2, col3 = st.columns([1, 1, 2])

                        with col1:
                            if st.button("❌ DELETAR", type="primary", key="confirm_delete"):
                                # Executar exclusão
                                delete_query = "DELETE FROM resultados_concursos WHERE id_concurso = ?"
                                success = db_manager.execute_command(delete_query, [id_busca])

                                if success:
                                    # Marcar jogo como não finalizado na tabela todos_os_jogos
                                    update_query = "UPDATE todos_os_jogos SET jogo_finalizado = 0 WHERE Resultado1 = ?"
                                    db_manager.execute_command(update_query, [jogo['resultado']])

                                    st.success(f"✅ Jogo {id_busca} deletado com sucesso!")
                                    st.balloons()
                                    st.rerun()
                                else:
                                    st.error("❌ Erro ao deletar o jogo")

                        with col2:
                            if st.button("🔙 Cancelar", type="secondary"):
                                st.rerun()
                    else:
                        st.error(f"❌ Jogo com ID {id_busca} não encontrado!")

    with tab2:
        st.subheader("📋 Últimos Jogos Cadastrados")

        # Mostrar últimos 10 jogos
        ultimos_jogos = db_manager.execute_query("""
            SELECT id_concurso, resultado, pares_impares, soma_numeros_resultaado
            FROM resultados_concursos
            ORDER BY id_concurso DESC
            LIMIT 10
        """)

        if not ultimos_jogos.empty:
            st.dataframe(ultimos_jogos, use_container_width=True)

            # Seletor para deletar da lista
            st.markdown("---")
            st.subheader("🗑️ Deletar da Lista")

            ids_disponiveis = ultimos_jogos['id_concurso'].tolist()
            id_selecionado = st.selectbox("Selecione o ID para deletar:", ids_disponiveis, key="select_delete")

            if st.button("🗑️ Deletar Selecionado", type="primary", key="delete_selected"):
                if id_selecionado:
                    # Buscar detalhes do jogo selecionado
                    jogo_selecionado = ultimos_jogos[ultimos_jogos['id_concurso'] == id_selecionado].iloc[0]

                    # Executar exclusão
                    delete_query = "DELETE FROM resultados_concursos WHERE id_concurso = ?"
                    success = db_manager.execute_command(delete_query, [id_selecionado])

                    if success:
                        # Marcar jogo como não finalizado
                        update_query = "UPDATE todos_os_jogos SET jogo_finalizado = 0 WHERE Resultado1 = ?"
                        db_manager.execute_command(update_query, [jogo_selecionado['resultado']])

                        st.success(f"✅ Jogo {id_selecionado} deletado com sucesso!")
                        st.balloons()
                        st.rerun()
                    else:
                        st.error("❌ Erro ao deletar o jogo")
        else:
            st.info("Nenhum jogo encontrado.")

# Consultar Resultados
elif selected_menu == "📊 Consultar Resultados":
    st.title("📊 Consultar Resultados")

    tab1, tab2 = st.tabs(["🏆 Resultados dos Concursos", "🎲 Jogos Possíveis"])

    with tab1:
        st.subheader("🏆 Resultados dos Concursos")

        col1, col2, col3 = st.columns(3)

        with col1:
            soma_min = st.number_input("Soma mínima:", min_value=120, max_value=300, value=150)
        with col2:
            soma_max = st.number_input("Soma máxima:", min_value=120, max_value=300, value=250)
        with col3:
            limite = st.number_input("Limite de resultados:", min_value=10, max_value=1000, value=50)

        if st.button("🔍 Buscar Resultados"):
            query = """
            SELECT id_concurso, resultado, pares_impares, soma_numeros_resultaado,
                   id_posicao_database, onda_variavel, onda_direcao
            FROM resultados_concursos
            WHERE soma_numeros_resultaado BETWEEN ? AND ?
            ORDER BY id_concurso DESC
            LIMIT ?
            """

            df_resultados = db_manager.execute_query(query, [soma_min, soma_max, limite])

            if not df_resultados.empty:
                st.dataframe(df_resultados, use_container_width=True)

                # Estatísticas
                st.subheader("📈 Estatísticas dos Resultados")
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("Total de Resultados", len(df_resultados))
                with col2:
                    st.metric("Soma Média", f"{df_resultados['soma_numeros_resultaado'].mean():.1f}")
                with col3:
                    st.metric("Amplitude", f"{df_resultados['soma_numeros_resultaado'].max() - df_resultados['soma_numeros_resultaado'].min()}")
            else:
                st.info("Nenhum resultado encontrado com os filtros aplicados.")

    with tab2:
        st.subheader("🎲 Jogos Possíveis")

        col1, col2 = st.columns(2)

        with col1:
            finalizado_filter = st.selectbox("Status:", ["Todos", "Finalizados", "Não Finalizados"])
        with col2:
            limite_jogos = st.number_input("Limite:", min_value=10, max_value=1000, value=100, key="limite_jogos")

        if st.button("🔍 Buscar Jogos"):
            where_clause = ""
            if finalizado_filter == "Finalizados":
                where_clause = "WHERE jogo_finalizado = 1"
            elif finalizado_filter == "Não Finalizados":
                where_clause = "WHERE jogo_finalizado = 0"

            query = f"""
            SELECT Resultado1, "Pares/Ímpares", Soma, jogo_finalizado
            FROM todos_os_jogos
            {where_clause}
            ORDER BY RANDOM()
            LIMIT ?
            """

            df_jogos = db_manager.execute_query(query, [limite_jogos])

            if not df_jogos.empty:
                st.dataframe(df_jogos, use_container_width=True)
            else:
                st.info("Nenhum jogo encontrado.")

# Análise de Jogos
elif selected_menu == "🎯 Análise de Jogos":
    st.title("🎯 Análise de Jogos")

    st.subheader("📊 Estatísticas Gerais")

    # Estatísticas dos jogos finalizados vs não finalizados
    stats_query = """
    SELECT
        jogo_finalizado,
        COUNT(*) as quantidade,
        AVG(Soma) as soma_media,
        MIN(Soma) as soma_min,
        MAX(Soma) as soma_max
    FROM todos_os_jogos
    GROUP BY jogo_finalizado
    """

    df_stats = db_manager.execute_query(stats_query)

    if not df_stats.empty:
        col1, col2 = st.columns(2)

        with col1:
            st.write("**🎲 Jogos Não Finalizados:**")
            nao_finalizados = df_stats[df_stats['jogo_finalizado'] == 0]
            if not nao_finalizados.empty:
                row = nao_finalizados.iloc[0]
                st.write(f"- Quantidade: {row['quantidade']:,}")
                st.write(f"- Soma Média: {row['soma_media']:.1f}")
                st.write(f"- Faixa: {row['soma_min']} - {row['soma_max']}")

        with col2:
            st.write("**✅ Jogos Finalizados:**")
            finalizados = df_stats[df_stats['jogo_finalizado'] == 1]
            if not finalizados.empty:
                row = finalizados.iloc[0]
                st.write(f"- Quantidade: {row['quantidade']:,}")
                st.write(f"- Soma Média: {row['soma_media']:.1f}")
                st.write(f"- Faixa: {row['soma_min']} - {row['soma_max']}")

# Configurações
elif selected_menu == "⚙️ Configurações":
    st.title("⚙️ Configurações")

    st.subheader("🗄️ Informações do Banco de Dados")

    # Verificar estrutura das tabelas
    tables_info = db_manager.execute_query("""
        SELECT name FROM sqlite_master WHERE type='table'
        ORDER BY name
    """)

    if not tables_info.empty:
        for table_name in tables_info['name']:
            with st.expander(f"📋 Tabela: {table_name}"):
                # Contar registros
                count_query = f"SELECT COUNT(*) as total FROM {table_name}"
                count_df = db_manager.execute_query(count_query)
                total = count_df.iloc[0]['total'] if not count_df.empty else 0

                st.write(f"**Total de registros:** {total:,}")

                # Mostrar estrutura
                pragma_query = f"PRAGMA table_info({table_name})"
                structure_df = db_manager.execute_query(pragma_query)

                if not structure_df.empty:
                    st.write("**Estrutura:**")
                    for _, col in structure_df.iterrows():
                        st.write(f"- {col['name']} ({col['type']})")

st.sidebar.markdown("---")
st.sidebar.info("🎲 Loto Fácil Manager v1.0")
