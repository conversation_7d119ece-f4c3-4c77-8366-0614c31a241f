import sqlite3
import pandas as pd

def demo_insert_game():
    """
    Demonstra como inserir um novo jogo e marcar como finalizado
    """
    
    print("🎲 DEMO: Inserindo novo jogo")
    print("=" * 50)
    
    conn = sqlite3.connect('database.sqlite')
    cursor = conn.cursor()
    
    try:
        # Exemplo de jogo para inserir
        id_concurso = 9999  # ID de teste
        resultado = "01,02,03,04,05,06,07,08,09,10,11,12,13,14,15"
        
        print(f"🎯 Inserindo concurso {id_concurso}")
        print(f"📊 Resultado: {resultado}")
        
        # Calcular dados automaticamente
        numbers = [int(n) for n in resultado.split(',')]
        
        # Pares/Ímpares
        pairs = sum(1 for n in numbers if n % 2 == 0)
        odds = 15 - pairs
        pares_impares = f"Par:{pairs} Impar:{odds}"
        
        # Soma
        soma = sum(numbers)
        
        # Encontrar posição na tabela todos_os_jogos
        cursor.execute("SELECT rowid FROM todos_os_jogos WHERE Resultado1 = ? LIMIT 1", (resultado,))
        result = cursor.fetchone()
        
        if result:
            posicao = result[0]
            print(f"✅ Jogo encontrado na posição: {posicao}")
            
            # Calcular onda (diferença da posição anterior)
            cursor.execute("""
                SELECT id_posicao_database 
                FROM resultados_concursos 
                WHERE id_posicao_database IS NOT NULL 
                ORDER BY id_concurso DESC 
                LIMIT 1
            """)
            
            last_pos_result = cursor.fetchone()
            if last_pos_result:
                last_position = last_pos_result[0]
                difference = posicao - last_position
                
                if difference > 0:
                    onda_valor = str(difference)
                    onda_direcao = "Subiu"
                elif difference < 0:
                    onda_valor = str(abs(difference))
                    onda_direcao = "Desceu"
                else:
                    onda_valor = "0"
                    onda_direcao = "Manteve"
            else:
                onda_valor = "0"
                onda_direcao = "Início"
            
            print(f"📈 Dados calculados:")
            print(f"   - Pares/Ímpares: {pares_impares}")
            print(f"   - Soma: {soma}")
            print(f"   - Posição: {posicao}")
            print(f"   - Onda: {onda_valor} ({onda_direcao})")
            
            # Verificar se já existe
            cursor.execute("SELECT id_concurso FROM resultados_concursos WHERE id_concurso = ?", (id_concurso,))
            if cursor.fetchone():
                print(f"⚠️  Concurso {id_concurso} já existe. Removendo para demo...")
                cursor.execute("DELETE FROM resultados_concursos WHERE id_concurso = ?", (id_concurso,))
                cursor.execute("UPDATE todos_os_jogos SET jogo_finalizado = 0 WHERE Resultado1 = ?", (resultado,))
            
            # Inserir novo resultado
            insert_query = """
            INSERT INTO resultados_concursos 
            (id_concurso, resultado, pares_impares, soma_numeros_resultaado, 
             id_posicao_database, onda_variavel, onda_direcao)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor.execute(insert_query, [
                id_concurso, resultado, pares_impares, soma,
                posicao, onda_valor, onda_direcao
            ])
            
            # Marcar jogo como finalizado
            cursor.execute("UPDATE todos_os_jogos SET jogo_finalizado = 1 WHERE Resultado1 = ?", (resultado,))
            
            conn.commit()
            
            print(f"✅ Jogo inserido com sucesso!")
            
            # Verificar resultado
            cursor.execute("""
                SELECT id_concurso, resultado, pares_impares, soma_numeros_resultaado,
                       id_posicao_database, onda_variavel, onda_direcao
                FROM resultados_concursos 
                WHERE id_concurso = ?
            """, (id_concurso,))
            
            result = cursor.fetchone()
            if result:
                print(f"\n📋 Resultado inserido:")
                print(f"   ID: {result[0]}")
                print(f"   Resultado: {result[1]}")
                print(f"   Pares/Ímpares: {result[2]}")
                print(f"   Soma: {result[3]}")
                print(f"   Posição: {result[4]}")
                print(f"   Onda: {result[5]} ({result[6]})")
            
            # Verificar se foi marcado como finalizado
            cursor.execute("SELECT jogo_finalizado FROM todos_os_jogos WHERE Resultado1 = ?", (resultado,))
            finalizado = cursor.fetchone()[0]
            print(f"   Finalizado: {'✅ Sim' if finalizado else '❌ Não'}")
            
        else:
            print(f"❌ Jogo não encontrado na tabela todos_os_jogos!")
    
    except Exception as e:
        print(f"❌ Erro: {e}")
        conn.rollback()
    
    finally:
        conn.close()

def show_stats():
    """Mostra estatísticas atuais"""
    
    print("\n📊 ESTATÍSTICAS ATUAIS")
    print("=" * 30)
    
    conn = sqlite3.connect('database.sqlite')
    
    # Total de jogos
    df = pd.read_sql_query("SELECT COUNT(*) as total FROM todos_os_jogos", conn)
    print(f"🎲 Total de jogos possíveis: {df.iloc[0]['total']:,}")
    
    # Jogos finalizados
    df = pd.read_sql_query("SELECT COUNT(*) as total FROM todos_os_jogos WHERE jogo_finalizado = 1", conn)
    print(f"✅ Jogos finalizados: {df.iloc[0]['total']:,}")
    
    # Total de concursos
    df = pd.read_sql_query("SELECT COUNT(*) as total FROM resultados_concursos", conn)
    print(f"🏆 Total de concursos: {df.iloc[0]['total']:,}")
    
    # Último concurso
    df = pd.read_sql_query("SELECT MAX(id_concurso) as ultimo FROM resultados_concursos", conn)
    ultimo = df.iloc[0]['ultimo']
    print(f"🎯 Último concurso: {ultimo}")
    
    conn.close()

if __name__ == "__main__":
    show_stats()
    print()
    demo_insert_game()
    print()
    show_stats()
