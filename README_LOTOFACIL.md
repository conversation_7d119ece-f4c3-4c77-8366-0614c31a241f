# 🎲 Loto Fácil - Gerenciador de Jogos

Sistema completo para gerenciamento de jogos da Loto Fácil com interface Streamlit.

## 📋 Funcionalidades

### ✅ **Principais Recursos:**

1. **➕ Inserir Novo Jogo**
   - Interface para inserir novos resultados de concursos
   - Cálculo automático de pares/ímpares, soma e posição no banco
   - Cálculo automático da "onda variável" (diferença de posição)
   - Marcação automática do jogo como finalizado

2. **📊 Dashboard**
   - Estatísticas gerais do sistema
   - Últimos resultados inseridos
   - Métricas de jogos finalizados vs não finalizados

3. **🔍 Consultas Avançadas**
   - Busca por resultados com filtros de soma
   - Visualização de jogos possíveis
   - Filtros por status (finalizado/não finalizado)

4. **🎯 Análise de Dados**
   - Estatísticas comparativas
   - Análise de padrões
   - Relatórios detalhados

## 🗄️ **Estrutura do Banco de Dados**

### Tabela: `todos_os_jogos`
- **Resultado1** (TEXT) - Combinação dos 15 números
- **Pares/Ímpares** (TEXT) - Distribuição par/ímpar
- **Soma** (INTEGER) - Soma dos números
- **jogo_finalizado** (BOOLEAN) - Se o jogo já saiu em algum concurso

### Tabela: `resultados_concursos`
- **id_concurso** (INTEGER) - ID do concurso
- **resultado** (TEXT) - Números sorteados
- **pares_impares** (TEXT) - Distribuição calculada automaticamente
- **soma_numeros_resultaado** (INTEGER) - Soma calculada automaticamente
- **id_posicao_database** (INTEGER) - Posição na tabela todos_os_jogos
- **onda_variavel** (TEXT) - Diferença de posição do último resultado
- **onda_direcao** (TEXT) - Se a onda subiu, desceu ou manteve

## 🚀 **Como Usar**

### 1. **Instalação**
```bash
# Instalar dependências
pip install -r requirements.txt

# Executar aplicação
streamlit run app_lotofacil.py
```

### 2. **Inserir Novo Jogo**

1. Acesse a aba "➕ Inserir Novo Jogo"
2. Digite o **ID do Concurso** (número sequencial)
3. Digite o **Resultado** no formato: `01,02,03,04,05,06,07,08,09,10,11,12,13,14,15`
4. Clique em "🚀 Processar e Inserir Jogo"
5. Revise os dados calculados automaticamente
6. Confirme a inserção

### 3. **Dados Calculados Automaticamente**

Quando você insere um novo jogo, o sistema calcula:

- **Pares/Ímpares**: Conta quantos números pares e ímpares (formato: "Par:8 Impar:7")
- **Soma**: Soma total dos 15 números
- **Posição no DB**: Localiza o jogo na tabela de 3+ milhões de combinações
- **Onda Variável**: Diferença de posição em relação ao último resultado
- **Direção da Onda**: Se subiu, desceu ou manteve a posição

### 4. **Exemplo Prático**

**Entrada:**
- ID Concurso: 3387
- Resultado: `01,02,03,04,05,06,07,08,09,10,11,12,13,14,15`

**Saída Automática:**
- Pares/Ímpares: `Par:7 Impar:8`
- Soma: `120`
- Posição: `1` (primeira combinação possível)
- Onda: `162586 (Desceu)` - desceu 162.586 posições em relação ao último

## 📊 **Estatísticas do Sistema**

- **Total de Jogos Possíveis**: 3.264.884 combinações
- **Concursos Cadastrados**: 3.386+ resultados históricos
- **Jogos Finalizados**: Marcados automaticamente quando inseridos

## 🎯 **Funcionalidades Especiais**

### **Validação Automática**
- Verifica se há exatamente 15 números
- Confirma números entre 1 e 25
- Detecta números duplicados
- Formata automaticamente (01, 02, etc.)

### **Busca Inteligente**
- Localiza o jogo na base de 3+ milhões de combinações
- Calcula posição exata no ranking
- Identifica padrões de movimento (ondas)

### **Interface Intuitiva**
- Dashboard com métricas em tempo real
- Filtros avançados para consultas
- Visualização clara dos dados
- Feedback visual para todas as operações

## 🔧 **Configurações**

### **Requisitos do Sistema**
- Python 3.8+
- Streamlit 1.28+
- Pandas 2.0+
- SQLite3 (incluído no Python)

### **Estrutura de Arquivos**
```
📁 DATABASE/
├── 📄 app_lotofacil.py          # Aplicação principal
├── 📄 database.sqlite           # Banco de dados
├── 📄 requirements.txt          # Dependências
├── 📄 demo_insert.py           # Script de demonstração
├── 📄 test_app.py              # Testes básicos
└── 📁 scripts/                 # Scripts auxiliares
    ├── consolidate_tables.py   # Consolidação das tabelas
    ├── check_new_structure.py  # Verificação da estrutura
    └── verify_consolidation.py # Verificação final
```

## 🎲 **Sobre a Loto Fácil**

A Loto Fácil é uma modalidade lotérica onde:
- São sorteados **15 números** de 1 a 25
- Existem **3.268.760 combinações possíveis** (nosso banco tem 3.264.884)
- Prêmios para 11, 12, 13, 14 e 15 acertos
- Sorteios realizados 3x por semana

## 🏆 **Vantagens do Sistema**

1. **Automação Total**: Todos os cálculos são feitos automaticamente
2. **Base Completa**: Mais de 3 milhões de combinações pré-calculadas
3. **Análise de Ondas**: Sistema único de tracking de posições
4. **Interface Moderna**: Streamlit com design responsivo
5. **Dados Históricos**: 3.386+ concursos já cadastrados
6. **Validação Robusta**: Verificações em todas as entradas

## 📞 **Suporte**

Para dúvidas ou problemas:
1. Verifique os logs no terminal
2. Execute `python test_app.py` para diagnóstico
3. Consulte a aba "⚙️ Configurações" na interface

---

**🎯 Sistema desenvolvido especificamente para análise e gerenciamento de jogos da Loto Fácil**
