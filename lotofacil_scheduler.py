import schedule
import time
from datetime import datetime, timedelta
from lotofacil_generator import LotofacilGenerator
from lotofacil_updater import LotofacilUpdater
import sqlite3
import threading

class LotofacilScheduler:
    def __init__(self, db_path='database.sqlite'):
        self.db_path = db_path
        self.generator = LotofacilGenerator(db_path)
        self.updater = LotofacilUpdater(db_path)
        self.running = False
    
    def log_evento(self, tipo: str, mensagem: str, sucesso: bool = True):
        """Registra eventos no log"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Criar tabela de log se não existir
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS log_eventos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    data_hora TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    tipo TEXT,
                    mensagem TEXT,
                    sucesso BOOLEAN
                )
            """)
            
            cursor.execute("""
                INSERT INTO log_eventos (tipo, mensagem, sucesso)
                VALUES (?, ?, ?)
            """, (tipo, mensagem, sucesso))
            
            conn.commit()
            conn.close()
            
            # Também imprimir no console
            status = "✅" if sucesso else "❌"
            print(f"{status} [{datetime.now().strftime('%d/%m/%Y %H:%M:%S')}] {tipo}: {mensagem}")
            
        except Exception as e:
            print(f"❌ Erro ao registrar log: {e}")
    
    def atualizar_base_dados(self):
        """Atualiza a base de dados com novos resultados"""
        try:
            self.log_evento("ATUALIZACAO", "Iniciando atualização da base de dados")
            
            sucesso, mensagem = self.updater.update_missing_contests()
            
            if sucesso:
                self.log_evento("ATUALIZACAO", f"Base atualizada: {mensagem}", True)
            else:
                self.log_evento("ATUALIZACAO", f"Erro na atualização: {mensagem}", False)
            
            return sucesso
            
        except Exception as e:
            self.log_evento("ATUALIZACAO", f"Erro inesperado: {e}", False)
            return False
    
    def gerar_sequencias_diarias(self):
        """Gera sequências para o próximo sorteio"""
        try:
            # Calcular data do próximo sorteio
            agora = datetime.now()
            proximo_sorteio = agora + timedelta(days=1)
            
            # Se amanhã for domingo, pular para segunda
            if proximo_sorteio.weekday() == 6:  # Domingo
                proximo_sorteio += timedelta(days=1)
            
            self.log_evento("GERACAO", f"Gerando sequências para {proximo_sorteio.strftime('%d/%m/%Y')}")
            
            sucesso = self.generator.gerar_sequencias_do_dia(proximo_sorteio)
            
            if sucesso:
                self.log_evento("GERACAO", "10 sequências geradas com sucesso", True)
            else:
                self.log_evento("GERACAO", "Erro ao gerar sequências", False)
            
            return sucesso
            
        except Exception as e:
            self.log_evento("GERACAO", f"Erro inesperado: {e}", False)
            return False
    
    def verificar_resultado_sorteio(self):
        """Verifica se saiu resultado do sorteio das 20:23"""
        try:
            self.log_evento("VERIFICACAO", "Verificando novos resultados após sorteio")
            
            # Atualizar base de dados
            sucesso = self.atualizar_base_dados()
            
            if sucesso:
                self.log_evento("VERIFICACAO", "Verificação concluída com sucesso", True)
            else:
                self.log_evento("VERIFICACAO", "Erro na verificação", False)
            
            return sucesso
            
        except Exception as e:
            self.log_evento("VERIFICACAO", f"Erro inesperado: {e}", False)
            return False
    
    def configurar_agendamentos(self):
        """Configura todos os agendamentos"""
        
        # Gerar sequências às 00:00 (segunda a sábado)
        schedule.every().monday.at("00:00").do(self.gerar_sequencias_diarias)
        schedule.every().tuesday.at("00:00").do(self.gerar_sequencias_diarias)
        schedule.every().wednesday.at("00:00").do(self.gerar_sequencias_diarias)
        schedule.every().thursday.at("00:00").do(self.gerar_sequencias_diarias)
        schedule.every().friday.at("00:00").do(self.gerar_sequencias_diarias)
        schedule.every().saturday.at("00:00").do(self.gerar_sequencias_diarias)
        
        # Verificar resultados às 20:30 (após o sorteio das 20:23)
        schedule.every().monday.at("20:30").do(self.verificar_resultado_sorteio)
        schedule.every().tuesday.at("20:30").do(self.verificar_resultado_sorteio)
        schedule.every().wednesday.at("20:30").do(self.verificar_resultado_sorteio)
        schedule.every().thursday.at("20:30").do(self.verificar_resultado_sorteio)
        schedule.every().friday.at("20:30").do(self.verificar_resultado_sorteio)
        schedule.every().saturday.at("20:30").do(self.verificar_resultado_sorteio)
        
        # Atualização geral da base às 06:00 (segunda a sábado)
        schedule.every().monday.at("06:00").do(self.atualizar_base_dados)
        schedule.every().tuesday.at("06:00").do(self.atualizar_base_dados)
        schedule.every().wednesday.at("06:00").do(self.atualizar_base_dados)
        schedule.every().thursday.at("06:00").do(self.atualizar_base_dados)
        schedule.every().friday.at("06:00").do(self.atualizar_base_dados)
        schedule.every().saturday.at("06:00").do(self.atualizar_base_dados)
        
        self.log_evento("SISTEMA", "Agendamentos configurados com sucesso")
    
    def iniciar_scheduler(self):
        """Inicia o scheduler em background"""
        self.running = True
        self.configurar_agendamentos()
        
        self.log_evento("SISTEMA", "Scheduler iniciado - aguardando agendamentos")
        
        def run_scheduler():
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # Verificar a cada minuto
        
        # Executar em thread separada
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        return scheduler_thread
    
    def parar_scheduler(self):
        """Para o scheduler"""
        self.running = False
        schedule.clear()
        self.log_evento("SISTEMA", "Scheduler parado")
    
    def status_agendamentos(self):
        """Retorna status dos agendamentos"""
        jobs = schedule.get_jobs()
        
        status = {
            'total_jobs': len(jobs),
            'proximo_job': None,
            'jobs_detalhes': []
        }
        
        if jobs:
            # Encontrar próximo job
            proximo = min(jobs, key=lambda job: job.next_run)
            status['proximo_job'] = {
                'funcao': proximo.job_func.__name__,
                'proximo_run': proximo.next_run.strftime('%d/%m/%Y %H:%M:%S')
            }
            
            # Detalhes de todos os jobs
            for job in jobs:
                status['jobs_detalhes'].append({
                    'funcao': job.job_func.__name__,
                    'agendamento': str(job),
                    'proximo_run': job.next_run.strftime('%d/%m/%Y %H:%M:%S')
                })
        
        return status
    
    def executar_agora(self, tipo: str):
        """Executa uma função específica agora (para testes)"""
        if tipo == "gerar":
            return self.gerar_sequencias_diarias()
        elif tipo == "atualizar":
            return self.atualizar_base_dados()
        elif tipo == "verificar":
            return self.verificar_resultado_sorteio()
        else:
            self.log_evento("ERRO", f"Tipo de execução inválido: {tipo}", False)
            return False

def main():
    """Função principal para teste"""
    scheduler = LotofacilScheduler()
    
    print("🕐 Testando Scheduler da Lotofácil")
    print("=" * 50)
    
    # Testar geração de sequências
    print("\n1. Testando geração de sequências...")
    sucesso = scheduler.executar_agora("gerar")
    print(f"Resultado: {'✅ Sucesso' if sucesso else '❌ Erro'}")
    
    # Testar atualização
    print("\n2. Testando atualização da base...")
    sucesso = scheduler.executar_agora("atualizar")
    print(f"Resultado: {'✅ Sucesso' if sucesso else '❌ Erro'}")
    
    # Mostrar status dos agendamentos
    print("\n3. Configurando agendamentos...")
    scheduler.configurar_agendamentos()
    
    status = scheduler.status_agendamentos()
    print(f"Total de agendamentos: {status['total_jobs']}")
    
    if status['proximo_job']:
        print(f"Próximo agendamento: {status['proximo_job']['funcao']} em {status['proximo_job']['proximo_run']}")
    
    print("\n4. Detalhes dos agendamentos:")
    for job in status['jobs_detalhes']:
        print(f"  - {job['funcao']}: {job['proximo_run']}")

if __name__ == "__main__":
    main()
